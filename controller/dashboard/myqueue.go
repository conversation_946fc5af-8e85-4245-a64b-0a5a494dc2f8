package dashboard

import (
	"context"
	"finbox/go-api/authentication"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/logger"
	workflowinstancesql "finbox/go-api/internal/repository/psql/workflowinstance"
	userinitializer "finbox/go-api/internal/service/datainitializer/user"
	"finbox/go-api/internal/service/myqueue"
	mdashboard "finbox/go-api/models/dashboard"
	"finbox/go-api/thirdparty/redgate"
	"fmt"
	"net/http"
)

func MyQueueData(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusInternalServerError)
		ctx := r.Context()
		attributes := r.Context().Value("attributes").(map[string]interface{})
		req := attributes["req"].(mdashboard.MyQueueRequestParam)

		var (
			dashboard  = ctx.Value("dashboard").(string)
			userType   = ctx.Value("user")
			lenderUser authentication.LenderUserStruct
		)

		switch v := userType.(type) {
		case authentication.LenderUserStruct:
			lenderUser = v
		case authentication.MasterDashboardUserStruct:
		default:
			logger.WithContext(ctx).Errorf("[MyQueueData] invalid user type in context")
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.InvalidDashboardUserMessage)
			return
		}

		var (
			branchCodes []string
			err         error
		)

		if lenderUser.LenderID == constants.MFLID {
			// Get branch codes
			branchCodes, err = fetchBranchCodes(ctx, r, req)
			if err != nil {
				logger.WithContext(ctx).Errorf("[MyQueueData] error fetching branch codes: %v, req: %+v", err, req)
				errorHandler.CustomError(w, http.StatusInternalServerError, mdashboard.ErrFetchingBranchCodes)
				return
			}
		}

		// Prepare and fetch queue data
		params := &myqueue.MyQueueLenderDashboardParams{
			LenderID:        lenderUser.LenderID,
			LoggedInUserID:  lenderUser.Email,
			DashboardType:   dashboard,
			WorkFlowTypes:   req.WorkFlowTypes,
			Page:            req.Page,
			PageSize:        req.PageSize,
			SerachType:      req.SerachType,
			SerachQuery:     req.SerachQuery,
			WorkflowStates:  req.WorkflowStates,
			CurrentAssignee: req.CurrentAssignee,
			BranchCodes:     branchCodes,
		}

		response, err := myqueue.FetchMyQueue(ctx, params)
		if err != nil {
			logger.WithContext(ctx).Errorf("[MyQueueData] error fetching queue: %v", err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		// Mock permissions for now , to add the code for policy permission here
		permissions := myqueue.Permissions{
			Actions: map[string]interface{}{
				"selfAssign": true,
				"assign":     true,
			},
		}
		for i := range response.LenderQueueDetails {
			response.LenderQueueDetails[i].Permissions = permissions
		}

		// Add response to context and proceed
		ctx = context.WithValue(ctx, "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func fetchBranchCodes(ctx context.Context, r *http.Request, req mdashboard.MyQueueRequestParam) ([]string, error) {
	// Get user initializer
	userInfoInitializer := userinitializer.GetUserClientType(constants.MFLLAPOrganizationID)
	if userInfoInitializer == nil {
		logger.WithContext(ctx).Warnf("[fetchBranchCodes] user initializer not found for MFLLAP organization")
		return []string{}, nil
	}

	// Fetch user info from RedGate
	authHeader := r.Header.Get("Authorization")
	if authHeader == "" {
		logger.WithContext(ctx).Error("[fetchBranchCodes] missing Authorization header")
		return nil, fmt.Errorf("missing Authorization header")
	}

	data, err := userInfoInitializer.FetchRedGateUserInfo(ctx, authHeader, constants.LenderDashboardRef)
	if err != nil {
		logger.WithContext(ctx).Errorf("[fetchBranchCodes] error fetching Redgate user: %v, data : %v", err, data)
		return nil, err
	}

	// Extract and validate branch code
	uniqueCode, ok := data["branchCode"].(string)
	if !ok || uniqueCode == "" {
		logger.WithContext(ctx).Error("[fetchBranchCodes] invalid or missing branchCode in Redgate user data")
		return nil, fmt.Errorf("invalid branchCode in user data")
	}
	sourceType := "PC Mortgage"
	lookupUniqueCode := uniqueCode
	if req.LookupUniqueCode != "" && req.SourceType != "" {
		lookupUniqueCode = req.LookupUniqueCode
		sourceType = req.SourceType
	}

	// Query hierarchy for branch children
	request := redgate.QueryHierarchyRequest{
		AppID:       constants.AppIdLendingDashboard,
		AppClientID: constants.MFLID,
		Hierarchy: redgate.HierarchyQuery{
			Metadata: map[string]string{"sourceType": sourceType},
		},
		LookupType:       "BRANCH",
		LookupUniqueCode: lookupUniqueCode,
		FindNodeType:     "CHILDREN",
	}

	nodeInfo, err := redgate.QueryHierarchyNode(ctx, &request)
	if err != nil {
		logger.WithContext(ctx).Errorf("[fetchBranchCodes] error querying hierarchy node: %v, request: %v , response : %v", err, request, nodeInfo)
		return nil, fmt.Errorf("failed to query hierarchy node: %w", err)
	}

	// Validate response structure
	if nodeInfo == nil {
		logger.WithContext(ctx).Error("[fetchBranchCodes] received nil response from QueryHierarchyNode")
		return nil, fmt.Errorf("received nil response from hierarchy query")
	}

	// Extract branch codes directly from the structured response
	branchCodes := make([]string, 0, len(nodeInfo.Children))
	for _, child := range nodeInfo.Children {
		if child.UniqueCode != "" {
			branchCodes = append(branchCodes, child.UniqueCode)
		} else {
			logger.WithContext(ctx).Warnf("[fetchBranchCodes] found child node with empty uniqueCode: %+v", child)
		}
	}

	return branchCodes, nil
}

func AssignWorkflow(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusInternalServerError)

		ctx := r.Context()
		attributes := ctx.Value("attributes").(map[string]interface{})
		req := attributes["req"].(mdashboard.AssignWorkflow)

		var (
			userType   = r.Context().Value("user")
			lenderUser authentication.LenderUserStruct
		)

		switch v := userType.(type) {
		case authentication.LenderUserStruct:
			lenderUser = v
		default:
			logger.WithContext(ctx).Error("[AssignWorkflow] invalid user type in context")
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.InvalidDashboardUserMessage)
			return
		}

		workflowRepo := workflowinstancesql.NewWorkflowInstancesRepository(database)

		// Fetch workflow instance
		instance, err := workflowRepo.DBGetWorkflowInstancesByParams(ctx, &workflowinstancesql.DBGetWorkflowInstancesParam{
			Id: req.WorkflowInstanceID,
		}, nil)
		if err != nil {
			logger.WithContext(ctx).Errorf("[AssignWorkflow] failed to get workflow instance: %s, error: %v", req.WorkflowInstanceID, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		// Check if already assigned
		if instance.AssignedTo == req.AssignTo {
			errorHandler.CustomError(w, http.StatusConflict, "This case is already assigned to the selected user")
			return
		}

		// Fetch latest history
		history, err := workflowRepo.DBGetLastWorkflowInstanceHistoryByParams(ctx, &workflowinstancesql.DBGetWorkflowInstanceHistoryParam{
			WorkflowInstanceId: req.WorkflowInstanceID,
		}, nil)
		if err != nil {
			logger.WithContext(ctx).Errorf("[AssignWorkflow] failed to fetch workflow history. err: %v, req: %v", err, req)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		tx, err := database.Beginx()
		if err != nil {
			logger.WithContext(ctx).Errorf("[AssignWorkflow] failed to begin tx. err: %v, req: %v", err, req)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		defer tx.Rollback()

		// Update workflow assignment
		updateParam := workflowinstancesql.DBUpdateWorkflowInstancesParam{
			WorkflowInstanceID: req.WorkflowInstanceID,
			AssignedTo:         req.AssignTo,
		}
		if err := workflowRepo.DBUpdateWorkflowInstances(ctx, tx, &updateParam); err != nil {
			logger.WithContext(ctx).Errorf("[AssignWorkflow] failed to update assignment. err: %v, req: %v", err, req)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		// Insert new workflow history
		insertParam := workflowinstancesql.DBInsertWorkflowInstanceHistoryParams{
			WorkflowInstanceID: req.WorkflowInstanceID,
			PreviousState:      history.PreviousState,
			CurrentState:       history.CurrentState,
			Action:             "case assign",
			ActionBy:           lenderUser.Email,
			AssignedGroup:      history.AssignedGroup,
			AssignedTo:         req.AssignTo,
			Remarks:            "Loan case assigned to user",
		}
		if err := workflowRepo.DBInsertWorkflowInstanceHistory(ctx, tx, &insertParam); err != nil {
			logger.WithContext(ctx).Errorf("[AssignWorkflow] failed to insert history. err: %v, req: %v", err, req)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		err = tx.Commit()
		if err != nil {
			logger.WithContext(ctx).Errorf("[AssignWorkflow] failed to commit during transist. err: %v, req: %v", err, req)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		ctx = context.WithValue(ctx, "resData", "success")
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
