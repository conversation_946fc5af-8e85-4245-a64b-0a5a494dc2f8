package dashboard

type MyQueueRequestParam struct {
	BranchCodes      []string `json:"branchCodes"`
	WorkFlowTypes    []string `json:"workflowTypes"`
	CurrentAssignee  string   `json:"currentAssignee"`
	SourceType       string   `json:"sourceType"`
	LookupUniqueCode string   `json:"lookupUniqueCode"`
	WorkflowStates   []string `json:"workflowState"`
	LenderID         string   `json:"lenderID"`
	WorkflowID       string   `json:"workflowID"`
	WorkflowName     string   `json:"workflowName"`
	DashboardType    string   `json:"dashboardType"`
	Page             int      `json:"page"`
	PageSize         int      `json:"pageSize"`
	SerachType       string   `json:"searchType"`
	SerachQuery      string   `json:"searchQuery"`
}

type AssignWorkflow struct {
	WorkflowInstanceID string `json:"workflowInstanceID"`
	AssignTo           string `json:"assignTo"`
}
