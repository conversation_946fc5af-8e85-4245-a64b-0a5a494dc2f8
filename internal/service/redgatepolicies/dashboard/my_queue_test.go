package dashboard

import (
	"context"
	"testing"

	"finbox/go-api/internal/service/myqueue"
)

func TestProcessMyQueuePermissions(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name           string
		policy         *GetMyQueuePolicy
		response       *myqueue.MyQueueResponse
		expectedCount  int
		expectedStates []string
	}{
		{
			name: "should filter visible states and add permissions",
			policy: &GetMyQueuePolicy{
				Data: map[string]MyQueueStateMetadata{
					"bcm_decision": {
						Actions: map[string]MyQueueActionMetadata{
							"assign": {
								IsVisible: false,
							},
							"self_assign": {
								IsVisible: true,
							},
						},
						IsVisible: true,
					},
					"in_principle_sanction": {
						Actions: map[string]MyQueueActionMetadata{
							"assign": {
								IsVisible: false,
							},
							"self_assign": {
								IsVisible: true,
							},
						},
						IsVisible: true,
					},
					"approved": {
						Actions:   map[string]MyQueueActionMetadata{},
						IsVisible: false,
					},
					"rejected": {
						Actions:   map[string]MyQueueActionMetadata{},
						IsVisible: false,
					},
				},
			},
			response: &myqueue.MyQueueResponse{
				LenderQueueDetails: []myqueue.LenderQueueDetail{
					{
						LoanApplicationID: "loan1",
						CurrentState:      "bcm_decision",
						Name:              "Test User 1",
					},
					{
						LoanApplicationID: "loan2",
						CurrentState:      "in_principle_sanction",
						Name:              "Test User 2",
					},
					{
						LoanApplicationID: "loan3",
						CurrentState:      "approved",
						Name:              "Test User 3",
					},
					{
						LoanApplicationID: "loan4",
						CurrentState:      "rejected",
						Name:              "Test User 4",
					},
					{
						LoanApplicationID: "loan5",
						CurrentState:      "unknown_state",
						Name:              "Test User 5",
					},
				},
				IsNextPageAvailable: true,
				TotalCount:          5,
			},
			expectedCount:  2,
			expectedStates: []string{"bcm_decision", "in_principle_sanction"},
		},
		{
			name:   "should return original response when policy is nil",
			policy: nil,
			response: &myqueue.MyQueueResponse{
				LenderQueueDetails: []myqueue.LenderQueueDetail{
					{
						LoanApplicationID: "loan1",
						CurrentState:      "bcm_decision",
						Name:              "Test User 1",
					},
				},
				IsNextPageAvailable: false,
				TotalCount:          1,
			},
			expectedCount:  1,
			expectedStates: []string{"bcm_decision"},
		},
		{
			name: "should return original response when response is nil",
			policy: &GetMyQueuePolicy{
				Data: map[string]MyQueueStateMetadata{},
			},
			response:       nil,
			expectedCount:  0,
			expectedStates: []string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ProcessMyQueuePermissions(ctx, tt.policy, tt.response)

			if tt.response == nil {
				if result != nil {
					t.Errorf("Expected nil result when response is nil, got %v", result)
				}
				return
			}

			if result == nil {
				t.Errorf("Expected non-nil result, got nil")
				return
			}

			if len(result.LenderQueueDetails) != tt.expectedCount {
				t.Errorf("Expected %d queue details, got %d", tt.expectedCount, len(result.LenderQueueDetails))
			}

			// Check if the expected states are present
			actualStates := make([]string, len(result.LenderQueueDetails))
			for i, detail := range result.LenderQueueDetails {
				actualStates[i] = detail.CurrentState
			}

			for _, expectedState := range tt.expectedStates {
				found := false
				for _, actualState := range actualStates {
					if actualState == expectedState {
						found = true
						break
					}
				}
				if !found {
					t.Errorf("Expected state %s not found in result", expectedState)
				}
			}

			// Check permissions are set correctly for visible states
			if tt.policy != nil {
				for _, detail := range result.LenderQueueDetails {
					if stateMetadata, exists := tt.policy.Data[detail.CurrentState]; exists && stateMetadata.IsVisible {
						if detail.Permissions.Actions == nil {
							t.Errorf("Expected permissions to be set for state %s", detail.CurrentState)
						}

						// Check specific actions
						for actionName, actionMetadata := range stateMetadata.Actions {
							if actionData, ok := detail.Permissions.Actions[actionName].(map[string]interface{}); ok {
								if isVisible, ok := actionData["isVisible"].(bool); ok {
									if isVisible != actionMetadata.IsVisible {
										t.Errorf("Expected action %s isVisible to be %v, got %v", actionName, actionMetadata.IsVisible, isVisible)
									}
								} else {
									t.Errorf("Expected isVisible to be bool for action %s", actionName)
								}
							} else {
								t.Errorf("Expected action %s to be present in permissions", actionName)
							}
						}
					}
				}
			}
		})
	}
}

func TestProcessQueueDetailActions(t *testing.T) {
	tests := []struct {
		name            string
		actions         map[string]MyQueueActionMetadata
		expectedActions map[string]interface{}
	}{
		{
			name: "should process actions correctly",
			actions: map[string]MyQueueActionMetadata{
				"assign": {
					IsVisible: false,
				},
				"self_assign": {
					IsVisible: true,
				},
			},
			expectedActions: map[string]interface{}{
				"assign": map[string]interface{}{
					"isVisible": false,
				},
				"self_assign": map[string]interface{}{
					"isVisible": true,
				},
			},
		},
		{
			name:            "should handle empty actions",
			actions:         map[string]MyQueueActionMetadata{},
			expectedActions: map[string]interface{}{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := processQueueDetailActions(tt.actions)

			if len(result.Actions) != len(tt.expectedActions) {
				t.Errorf("Expected %d actions, got %d", len(tt.expectedActions), len(result.Actions))
			}

			for actionName, expectedAction := range tt.expectedActions {
				if actualAction, exists := result.Actions[actionName]; exists {
					if actualActionMap, ok := actualAction.(map[string]interface{}); ok {
						if expectedActionMap, ok := expectedAction.(map[string]interface{}); ok {
							for key, expectedValue := range expectedActionMap {
								if actualValue, keyExists := actualActionMap[key]; keyExists {
									if actualValue != expectedValue {
										t.Errorf("Expected action %s.%s to be %v, got %v", actionName, key, expectedValue, actualValue)
									}
								} else {
									t.Errorf("Expected key %s to exist in action %s", key, actionName)
								}
							}
						}
					}
				} else {
					t.Errorf("Expected action %s to exist", actionName)
				}
			}
		})
	}
}
