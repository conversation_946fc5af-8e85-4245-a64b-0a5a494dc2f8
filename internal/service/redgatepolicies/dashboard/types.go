package dashboard

type GetDocumentPolicy struct {
	Documents struct {
		Category map[string]struct {
			DropDown struct {
				IsVisible bool `json:"isVisible"`
			} `json:"dropDown"`
			DropDownOptions map[string]bool `json:"dropDownOptions"`
		} `json:"category"`
	} `json:"documents"`
}

type GetTaskPolicy struct {
	TaskTypes map[string]interface{} `json:"taskTypes"`
	Buttons   map[string]interface{} `json:"buttons"`
}

type PermissionConditionType string

const (
	AssignedUser        PermissionConditionType = "assignedUser"
	TaskAssigned        PermissionConditionType = "taskAssigned"
	CreatedBy           PermissionConditionType = "createdBy"
	CurrentUser         PermissionConditionType = "currentUser"
	UserType            PermissionConditionType = "userType"
	IsVisible           PermissionConditionType = "isVisible"
	CurrentUserAndAbove PermissionConditionType = "currentUserAndAbove"
)

// GetLoanDeviationPolicy represents the structure of loan deviation policy response from RedGate
type GetLoanDeviationPolicy struct {
	Status map[string]StatusMetadata `json:"status"`
}

// StatusMetadata represents the metadata for a specific status
type StatusMetadata struct {
	Buttons map[string]ButtonMetadata `json:"buttons"`
	Fields  map[string]FieldMetadata  `json:"fields"`
}

// ButtonMetadata represents button configuration with conditions
type ButtonMetadata struct {
	Condition ConditionMetadata `json:"condition"`
}

// FieldMetadata represents field configuration with conditions
type FieldMetadata struct {
	Condition ConditionMetadata `json:"condition,omitempty"`
	IsVisible bool              `json:"isVisible,omitempty"`
}

// ConditionMetadata represents various condition types
type ConditionMetadata map[string]interface{}

// GetMyQueuePolicy represents the structure of my queue policy response from RedGate
type GetMyQueuePolicy struct {
	Data map[string]MyQueueStateMetadata `json:"data"`
}

// MyQueueStateMetadata represents the metadata for a specific workflow state
type MyQueueStateMetadata struct {
	Actions   map[string]MyQueueActionMetadata `json:"actions"`
	IsVisible bool                             `json:"isVisible"`
}

// MyQueueActionMetadata represents action configuration with visibility
type MyQueueActionMetadata struct {
	IsVisible bool `json:"isVisible"`
}

type TaskPermissionRequest struct {
	TaskTypeName      string
	StatusName        string
	TaskAssignedUser  string
	LogInUserType     string
	LogInUserEmail    string
	CreatedBy         string
	LenderID          string
	BearerToken       string
	UserID            string
	LoanApplicationID string
}
