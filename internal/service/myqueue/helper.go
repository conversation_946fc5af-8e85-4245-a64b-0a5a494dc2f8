package myqueue

import "github.com/jmoiron/sqlx"

// WithBranchCodeCondition handles branch code filtering
func WithBranchCodeCondition(params *MyQueueLenderDashboardParams) (*QueryCondition, error) {
	if params != nil && len(params.BranchCodes) == 0 {
		return nil, nil
	}

	query, args, err := sqlx.In("(u.dynamic_user_info::jsonb->>'fdgl_code') IN (?)", params.BranchCodes)
	if err != nil {
		return nil, err
	}

	return &QueryCondition{
		Condition: query,
		Args:      args,
	}, nil
}

// WithAssigneeUserCondition handles assignee filtering
func WithAssigneeUserCondition(params *MyQueueLenderDashboardParams) *QueryCondition {
	if params != nil && params.CurrentAssignee == "" {
		return nil
	}

	return &QueryCondition{
		Condition: `wi.assigned_to = ?`,
		Args:      []interface{}{params.CurrentAssignee},
	}
}

// GetWorkflowStateCondition handles workflow status filtering
func GetWorkflowStateCondition(params *MyQueueLenderDashboardParams) (*QueryCondition, error) {
	if params != nil && len(params.WorkflowStates) == 0 {
		return nil, nil
	}

	query, args, err := sqlx.In(" wi.current_state IN (?)", params.WorkflowStates)
	if err != nil {
		return nil, err
	}

	return &QueryCondition{
		Condition: query,
		Args:      args,
	}, nil

}

// GetCustomFilterCondition handles user-defined search filters dynamically
func GetCustomFilterCondition(params *MyQueueLenderDashboardParams) *QueryCondition {
	if params == nil || params.SerachQuery == "" || params.SerachType == "" {
		return nil
	}

	var column string

	switch params.SerachType {
	case SearchTypeMobile:
		column = "u.mobile"
	case SearchTypeEmail:
		column = "u.email"
	case SearchTypeName:
		column = "u.name"
	case SearchTypeCustomerID:
		column = "u.crm_id"
	case SearchTypePartnerCode:
		column = "u.partner_code"
	case SearchTypeLoanApplicationNo:
		column = "la.loan_application_no"
	case SearchTypeLoanAssignTo:
		return WithAssigneeUserCondition(params)
	case SearchTypeWorkflowState:
		cond, _ := GetWorkflowStateCondition(params)
		return cond
	default:
		return nil
	}

	return &QueryCondition{
		Condition: column + " ILIKE ?",
		Args:      []interface{}{"%" + params.SerachQuery + "%"},
	}
}
