package authProviders

import (
	"context"
	"finbox/go-api/constants"
	"finbox/go-api/thirdparty/redgate"
)

type Authorizer interface {
	AuthorizeUser(ctx context.Context, authRequest redgate.AuthorizeUserRequest) (*redgate.AuthorizationResponse, error)
	ExecutePolicy(ctx context.Context, policyRequest redgate.ExecutePolicyRequest) (*redgate.ExecutePolicyResponse, error)

	ExecutePolicyWithoutUserInfo(ctx context.Context, policyRequest redgate.ExecutePolicyRequest) (*redgate.ExecutePolicyResponse, error)
}

func NewAuthorizer(lenderID string) Authorizer {
	switch lenderID {
	case constants.MFLID:
		return &MflAuthorizer{}
	}
	return nil
}

type MflAuthorizer struct{}
